#include <Arduino.h>
#include <FlexCAN_T4.h> // Using FlexCAN_T4 for Teen<PERSON>'s native CAN [3]

// --- Teensy 4.1 Native CAN Configuration ---
// CAN1 uses pins 22 (TX) and 23 (RX) on Teensy 4.1 [4]
FlexCAN_T4<CAN1, RX_SIZE_256, TX_SIZE_16> can1; 

// --- Kinco FD1X5 Specific Constants ---
// IMPORTANT: Verify these values for your specific motor and drive model.
// Encoder_Resolution for motorID = 1 [User Input]
const float ENCODER_RESOLUTION = 10000.0f; 
// IPEAK_VALUE for FD124S-CB-000 drive model [1]
const float IPEAK_VALUE = 48.0f; 

// --- CANopen SDO Constants ---
// SDO Command Bytes [2]
const uint8_t SDO_READ_CMD = 0x40;
const uint8_t SDO_WRITE_1BYTE_CMD = 0x2F;
const uint8_t SDO_WRITE_2BYTE_CMD = 0x2B;
const uint8_t SDO_WRITE_4BYTE_CMD = 0x23;
const uint8_t SDO_ABORT_CMD = 0x80;
const uint8_t SDO_READ_4BYTE_RESP = 0x43; // For Integer32 data [2]
const uint8_t SDO_READ_2BYTE_RESP = 0x4B; // For Integer16 data [2]
const uint8_t SDO_READ_1BYTE_RESP = 0x4F; // For Integer8 data [2]

// --- CANopen Object Dictionary Indices ---
// Controlword (0x6040) [2]
const uint16_t CONTROLWORD_INDEX = 0x6040;
const uint8_t CONTROLWORD_SUBINDEX = 0x00;
// Statusword (0x6041) [2]
const uint16_t STATUSWORD_INDEX = 0x6041;
const uint8_t STATUSWORD_SUBINDEX = 0x00;
// Actual velocity (RPM) (0x60F9.00) [2]
const uint16_t ACTUAL_VELOCITY_INDEX = 0x60F9;
const uint8_t ACTUAL_VELOCITY_SUBINDEX = 0x00;
// Current_OUT_Real (Current consumption) (0x60F5.08) [2]
const uint16_t CURRENT_OUT_REAL_INDEX = 0x60F5;
const uint8_t CURRENT_OUT_REAL_SUBINDEX = 0x08;

// --- DS402 Statusword Bitmasks for State Checking --- [2]
const uint16_t STATUS_READY_TO_SWITCH_ON = 0x0041; // bit0=1, bit1=0, bit2=0, bit6=1
const uint16_t STATUS_SWITCHED_ON = 0x0021;       // bit0=1, bit1=1, bit2=0, bit6=0
const uint16_t STATUS_OPERATION_ENABLED = 0x0023;   // bit0=1, bit1=1, bit2=1, bit6=0
const uint16_t STATUS_FAULT = 0x0008;             // bit3=1

// --- Function Prototypes ---
bool sendSDOWrite(uint8_t nodeID, uint16_t index, uint8_t subIndex, uint32_t data, uint8_t dataLengthBytes);
bool readSDO(uint8_t nodeID, uint16_t index, uint8_t subIndex, uint32_t& receivedData, uint8_t& responseCmd);
bool checkStatusWord(uint8_t nodeID, uint16_t expectedStatus, unsigned long timeoutMs);

// --- Setup Function ---
void setup() {
  Serial.begin(115200);
  while (!Serial && millis() < 5000); // Wait for serial port to connect (for Teensy)

  // Initialize CAN bus for CAN1 (pins 22/23) at 1000 KBit/s [4, 2]
  can1.begin();
  can1.setBaudRate(1000000); // 1000 KBit/s = 1 Mbit/s [2]

  if (can1.isInitialized()) {
    Serial.println("CAN bus (CAN1) initialized successfully at 1000 KBit/s.");
  } else {
    Serial.println("Error initializing CAN bus (CAN1)!");
    while (1); // Halt on error
  }

  // --- Kinco Drive DS402 State Transition Sequence --- [2]
  uint8_t motorDriverNodeID = 1; // Default Node ID for Kinco drive [2]

  Serial.println("Attempting to transition Kinco drive to 'Operation Enabled' state...");

  // 1. Send Shutdown (0x06) [2]
  Serial.println("Sending Shutdown (0x06)...");
  if (!sendSDOWrite(motorDriverNodeID, CONTROLWORD_INDEX, CONTROLWORD_SUBINDEX, 0x06, 2)) {
    Serial.println("Failed to send Shutdown command.");
    return;
  }
  if (!checkStatusWord(motorDriverNodeID, STATUS_READY_TO_SWITCH_ON, 2000)) {
    Serial.println("Failed to reach 'Ready to switch on' state after Shutdown.");
    return;
  }
  Serial.println("Reached 'Ready to switch on' state.");

  // 2. Send Switch On (0x07) [2]
  Serial.println("Sending Switch On (0x07)...");
  if (!sendSDOWrite(motorDriverNodeID, CONTROLWORD_INDEX, CONTROLWORD_SUBINDEX, 0x07, 2)) {
    Serial.println("Failed to send Switch On command.");
    return;
  }
  if (!checkStatusWord(motorDriverNodeID, STATUS_SWITCHED_ON, 2000)) {
    Serial.println("Failed to reach 'Switched on' state after Switch On.");
    return;
  }
  Serial.println("Reached 'Switched on' state.");

  // 3. Send Enable Operation (0x0F) [2]
  Serial.println("Sending Enable Operation (0x0F)...");
  if (!sendSDOWrite(motorDriverNodeID, CONTROLWORD_INDEX, CONTROLWORD_SUBINDEX, 0x0F, 2)) {
    Serial.println("Failed to send Enable Operation command.");
    return;
  }
  if (!checkStatusWord(motorDriverNodeID, STATUS_OPERATION_ENABLED, 2000)) {
    Serial.println("Failed to reach 'Operation Enabled' state after Enable Operation.");
    return;
  }
  Serial.println("Kinco drive is now 'Operation Enabled'.");
}

// --- fetchMotorData Function ---
void fetchMotorData(uint8_t motorDriverNodeID) {
  Serial.print("\n--- Fetching data for Motor ID: ");
  Serial.print(motorDriverNodeID);
  Serial.println(" ---");

  uint32_t receivedData = 0;
  uint8_t responseCmd = 0;

  // --- Fetch RPM (Actual velocity) --- [2]
  if (readSDO(motorDriverNodeID, ACTUAL_VELOCITY_INDEX, ACTUAL_VELOCITY_SUBINDEX, receivedData, responseCmd)) {
    if (responseCmd == SDO_READ_4BYTE_RESP) { // Expected for Integer32 [2]
      int32_t raw_rpm_dec = (int32_t)receivedData;
      // RPM conversion formula [2]
      float rpm = (float)raw_rpm_dec * 1875.0f / (512.0f * ENCODER_RESOLUTION);
      Serial.print("RPM: ");
      Serial.println(rpm, 2);
    } else if (responseCmd == SDO_READ_1BYTE_RESP) { // Observed unexpected 1-byte response [2]
      int8_t raw_rpm_dec_8bit = (int8_t)receivedData;
      Serial.print("WARNING: Received 1-byte SDO response (0x4F) for RPM (expected 4-byte 0x43). Raw value: ");
      Serial.print(raw_rpm_dec_8bit);
      // Attempt to apply conversion, but note potential inaccuracy due to 1-byte data
      float rpm = (float)raw_rpm_dec_8bit * 1875.0f / (512.0f * ENCODER_RESOLUTION);
      Serial.print(". Converted RPM (may be inaccurate): ");
      Serial.println(rpm, 2);
    } else if (responseCmd == SDO_ABORT_CMD) {
      Serial.print("SDO Abort for RPM. Error Code: 0x");
      Serial.println(receivedData, HEX);
    } else {
      Serial.print("Unexpected SDO response command for RPM: 0x");
      Serial.println(responseCmd, HEX);
    }
  } else {
    Serial.println("Failed to read RPM data (timeout or communication error).");
  }

  // --- Fetch Current Consumption (Current_OUT_Real) --- [2]
  if (readSDO(motorDriverNodeID, CURRENT_OUT_REAL_INDEX, CURRENT_OUT_REAL_SUBINDEX, receivedData, responseCmd)) {
    if (responseCmd == SDO_READ_2BYTE_RESP) { // Expected for Integer16 [2]
      int16_t raw_current_dec = (int16_t)receivedData; // Current_OUT_Real is Integer16 [2]
      // Current conversion formula [2]
      float current_arms = (float)raw_current_dec * (IPEAK_VALUE / 1.414f) / 2048.0f;
      Serial.print("Current (Arms): ");
      Serial.println(current_arms, 2);
    } else if (responseCmd == SDO_ABORT_CMD) {
      Serial.print("SDO Abort for Current. Error Code: 0x");
      Serial.println(receivedData, HEX);
    } else {
      Serial.print("Unexpected SDO response command for Current: 0x");
      Serial.println(responseCmd, HEX);
    }
  } else {
    Serial.println("Failed to read Current data (timeout or communication error).");
  }
}

// --- Helper Functions ---

// Function to send SDO write command
bool sendSDOWrite(uint8_t nodeID, uint16_t index, uint8_t subIndex, uint32_t data, uint8_t dataLengthBytes) {
  CAN_message_t outgoing_frame;
  outgoing_frame.id = 0x600 + nodeID; // Client to Server SDO COB-ID [2]
  outgoing_frame.len = 8;

  uint8_t cmd_byte;
  if (dataLengthBytes == 1) {
    cmd_byte = SDO_WRITE_1BYTE_CMD;
  } else if (dataLengthBytes == 2) {
    cmd_byte = SDO_WRITE_2BYTE_CMD;
  } else if (dataLengthBytes == 4) {
    cmd_byte = SDO_WRITE_4BYTE_CMD;
  } else {
    Serial.println("Invalid data length for SDO write.");
    return false;
  }

  outgoing_frame.buf = cmd_byte;
  outgoing_frame.buf[2] = (uint8_t)(index & 0xFF);       // Index LSB [2]
  outgoing_frame.buf[4] = (uint8_t)((index >> 8) & 0xFF); // Index MSB [2]
  outgoing_frame.buf[3] = subIndex;

  // Data payload (LSB first) [2] - starts from buf[1]
  outgoing_frame.buf[1] = (uint8_t)(data & 0xFF);
  outgoing_frame.buf = (uint8_t)((data >> 8) & 0xFF);
  outgoing_frame.buf = (uint8_t)((data >> 16) & 0xFF);
  outgoing_frame.buf = (uint8_t)((data >> 24) & 0xFF);

  if (can1.write(outgoing_frame)) {
    // Wait for SDO response (0x60 for success, 0x80 for abort) [2]
    unsigned long startTime = millis();
    while (millis() - startTime < 1000) { // 1 second timeout
      CAN_message_t incoming_frame;
      if (can1.read(incoming_frame)) {
        if (incoming_frame.id == (0x580 + nodeID) && incoming_frame.len == 8) { // Server to Client SDO COB-ID [2]
          if (incoming_frame.buf == 0x60) { // Successful write response [2]
            return true;
          } else if (incoming_frame.buf == SDO_ABORT_CMD) { // SDO Abort [2]
            uint32_t errorCode = (uint32_t)incoming_frame.buf[1] |
                                 ((uint32_t)incoming_frame.buf << 8) |
                                 ((uint32_t)incoming_frame.buf << 16) |
                                 ((uint32_t)incoming_frame.buf << 24);
            Serial.print("SDO Write Abort: 0x");
            Serial.println(errorCode, HEX);
            return false;
          }
        }
      }
    }
    Serial.println("SDO Write response timeout.");
    return false;
  } else {
    Serial.println("Failed to send SDO Write frame.");
    return false;
  }
}

// Function to send SDO read command and receive data
bool readSDO(uint8_t nodeID, uint16_t index, uint8_t subIndex, uint32_t& receivedData, uint8_t& responseCmd) {
  CAN_message_t outgoing_frame;
  outgoing_frame.id = 0x600 + nodeID; // Client to Server SDO COB-ID [2]
  outgoing_frame.len = 8;
  outgoing_frame.buf = SDO_READ_CMD; // SDO read command [2]
  outgoing_frame.buf[2] = (uint8_t)(index & 0xFF);       // Index LSB [2]
  outgoing_frame.buf[4] = (uint8_t)((index >> 8) & 0xFF); // Index MSB [2]
  outgoing_frame.buf[3] = subIndex;
  outgoing_frame.buf[1] = 0x00; // Reserved
  outgoing_frame.buf = 0x00; // Reserved
  outgoing_frame.buf = 0x00; // Reserved
  outgoing_frame.buf = 0x00; // Reserved

  if (can1.write(outgoing_frame)) {
    unsigned long startTime = millis();
    while (millis() - startTime < 1000) { // 1 second timeout
      CAN_message_t incoming_frame;
      if (can1.read(incoming_frame)) {
        if (incoming_frame.id == (0x580 + nodeID) && incoming_frame.len == 8) { // Server to Client SDO COB-ID [2]
          responseCmd = incoming_frame.buf;
          if (responseCmd == SDO_READ_4BYTE_RESP) { // 0x43 for Integer32 [2]
            receivedData = (uint32_t)incoming_frame.buf[1] |
                           ((uint32_t)incoming_frame.buf << 8) |
                           ((uint32_t)incoming_frame.buf << 16) |
                           ((uint32_t)incoming_frame.buf << 24);
            return true;
          } else if (responseCmd == SDO_READ_2BYTE_RESP) { // 0x4B for Integer16 [2]
            receivedData = (uint16_t)incoming_frame.buf[1] |
                           ((uint16_t)incoming_frame.buf << 8);
            return true;
          } else if (responseCmd == SDO_READ_1BYTE_RESP) { // 0x4F for Integer8 [2]
            receivedData = (uint8_t)incoming_frame.buf[1];
            return true;
          } else if (responseCmd == SDO_ABORT_CMD) { // 0x80 for SDO Abort [2]
            receivedData = (uint32_t)incoming_frame.buf[1] |
                           ((uint32_t)incoming_frame.buf << 8) |
                           ((uint32_t)incoming_frame.buf << 16) |
                           ((uint32_t)incoming_frame.buf << 24);
            return true; // Return true, but responseCmd indicates abort
          }
        }
      }
    }
    return false; // Timeout
  } else {
    Serial.println("Failed to send SDO Read frame.");
    return false;
  }
}

// Function to check the Statusword until an expected state is reached or timeout occurs
bool checkStatusWord(uint8_t nodeID, uint16_t expectedStatus, unsigned long timeoutMs) {
  unsigned long startTime = millis();
  uint32_t currentStatusWord = 0;
  uint8_t responseCmd = 0;

  while (millis() - startTime < timeoutMs) {
    if (readSDO(nodeID, STATUSWORD_INDEX, STATUSWORD_SUBINDEX, currentStatusWord, responseCmd)) {
      if (responseCmd == SDO_READ_2BYTE_RESP) { // Statusword is Unsigned16 [2]
        // Check for fault state first [2]
        if ((currentStatusWord & STATUS_FAULT) == STATUS_FAULT) {
          Serial.print("Drive entered Fault state (0x");
          Serial.print(currentStatusWord, HEX);
          Serial.println("). Attempting to reset fault (0x80)...");
          sendSDOWrite(nodeID, CONTROLWORD_INDEX, CONTROLWORD_SUBINDEX, 0x80, 2); // Send Fault Reset [2]
          delay(100); // Give time for reset
          return false; // After fault reset, it should go to Switch on disabled, then proceed with normal sequence
        }
        
        // Check if the expected bits are set and other relevant bits are clear [2]
        bool match = false;
        if (expectedStatus == STATUS_READY_TO_SWITCH_ON) {
          match = ((currentStatusWord & 0x0041) == 0x0041) && ((currentStatusWord & 0x0006) == 0x0000);
        } else if (expectedStatus == STATUS_SWITCHED_ON) {
          match = ((currentStatusWord & 0x0021) == 0x0021) && ((currentStatusWord & 0x0040) == 0x0000);
        } else if (expectedStatus == STATUS_OPERATION_ENABLED) {
          match = ((currentStatusWord & 0x0023) == 0x0023) && ((currentStatusWord & 0x0040) == 0x0000);
        }

        if (match) {
          return true;
        }
      } else if (responseCmd == SDO_ABORT_CMD) {
        Serial.print("SDO Abort while checking Statusword: 0x");
        Serial.println(currentStatusWord, HEX);
        return false;
      }
    }
    delay(50); // Small delay before polling again
  }
  return false; // Timeout
}

// --- Loop Function ---
void loop() {
  fetchMotorData(1); // Fetch data for motor with Node ID 1
  delay(1000);       // Poll every 1 second
}